<template>
  <div class="panel-card">
    <div class="panel-header">色彩调整</div>

    <!-- 基础牙齿颜色 -->
    <div class="color-selector">
      <div class="slider-label">基础色</div>
      <el-color-picker
        v-model="baseToothColor"
        size="small"
        show-alpha
        :predefine="predefineColors"
        popper-class="tooth-color-picker"
      />
    </div>

    <!-- 色相调整 -->
    <div class="slider-container hue-slider">
      <div class="slider-label">色相</div>
      <div class="hue-slider-wrapper" ref="hueSliderWrapperRef">
        <el-slider
          v-model="hueValue"
          :min="-180"
          :max="180"
          :step="1"
          :format-tooltip="(val: number) => `${val}°`"
          class="hue-slider-component"
          ref="hueSliderRef"
        />
      </div>
    </div>

    <!-- 饱和度调整 -->
    <div class="slider-container">
      <div class="slider-label">饱和度</div>
      <el-slider v-model="saturationValue" :min="-1" :max="1" :step="0.01" />
    </div>

    <!-- 亮度调整 -->
    <div class="slider-container">
      <div class="slider-label">亮度</div>
      <el-slider
        v-model="brightnessValue"
        :min="-1"
        :max="0.2"
        :step="0.005"
        :show-tooltip="false"
      />
    </div>

    <!-- 纹理强度 -->
    <div class="slider-container">
      <div class="slider-label">纹理强度</div>
      <el-slider v-model="textureStrengthValue" :min="0.4" :max="1" :step="0.01" />
    </div>

    <!-- 重置按钮 -->
    <div class="reset-button-container">
      <el-button size="small" @click="resetValues">重置色彩</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, watch, nextTick, ref } from 'vue'

// 预定义牙齿颜色
const predefineColors = [
  '#FFFAF0', // 天然象牙白
  '#F8F6F0', // 自然牙色
  '#F5F2E8', // 稍淡牙色
  '#F0E6D2', // 淡黄牙色
  '#E6D8C8', // 轻度黄染牙色
  '#D8C7B4', // 中度黄染牙色
  '#C8B6A3' // 重度黄染牙色
]

// 色相滑块引用
const hueSliderWrapperRef = ref<HTMLElement | null>(null)
const hueSliderRef = ref<any>(null)

/**
 * 将十六进制颜色转换为HSL
 */
function hexToHsl(hex: string): { h: number; s: number; l: number } {
  // 移除 # 符号
  const cleanHex = hex.replace('#', '')

  // 转换为RGB，使用substring替代已废弃的substr
  const r = parseInt(cleanHex.substring(0, 2), 16) / 255
  const g = parseInt(cleanHex.substring(2, 4), 16) / 255
  const b = parseInt(cleanHex.substring(4, 6), 16) / 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h = 0
  let s = 0
  const l = (max + min) / 2

  if (max !== min) {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)

    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0)
        break
      case g:
        h = (b - r) / d + 2
        break
      case b:
        h = (r - g) / d + 4
        break
    }
    h /= 6
  }

  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100)
  }
}

/**
 * 将HSL转换为十六进制颜色
 */
function hslToHex(h: number, s: number, l: number): string {
  h = h / 360
  s = s / 100
  l = l / 100

  const hue2rgb = (p: number, q: number, t: number) => {
    if (t < 0) t += 1
    if (t > 1) t -= 1
    if (t < 1 / 6) return p + (q - p) * 6 * t
    if (t < 1 / 2) return q
    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6
    return p
  }

  let r, g, b

  if (s === 0) {
    r = g = b = l // achromatic
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s
    const p = 2 * l - q
    r = hue2rgb(p, q, h + 1 / 3)
    g = hue2rgb(p, q, h)
    b = hue2rgb(p, q, h - 1 / 3)
  }

  const toHex = (c: number) => {
    const hex = Math.round(c * 255).toString(16)
    return hex.length === 1 ? '0' + hex : hex
  }

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`
}

// Props
const props = defineProps({
  hue: {
    type: Number,
    default: 0
  },
  saturation: {
    type: Number,
    default: 0
  },
  brightness: {
    type: Number,
    default: 0
  },
  textureStrength: {
    type: Number,
    default: 0.7
  },
  baseColor: {
    type: String,
    default: '#FFFAF0' // 默认象牙白色
  }
})

// Emits
const emit = defineEmits([
  'update:hue',
  'update:saturation',
  'update:brightness',
  'update:textureStrength',
  'update:baseColor'
])

// 计算属性 - 用于v-model双向绑定
const hueValue = computed({
  get: () => props.hue,
  set: (value) => emit('update:hue', value)
})

const saturationValue = computed({
  get: () => props.saturation,
  set: (value) => emit('update:saturation', value)
})

const brightnessValue = computed({
  get: () => props.brightness,
  set: (value) => emit('update:brightness', value)
})

const textureStrengthValue = computed({
  get: () => props.textureStrength,
  set: (value) => emit('update:textureStrength', value)
})

const baseToothColor = computed({
  get: () => props.baseColor,
  set: (value) => emit('update:baseColor', value)
})

// 计算基于当前基础色的动态色相渐变
const dynamicHueGradient = computed(() => {
  // 获取基础色的HSL值
  const baseHsl = hexToHsl(props.baseColor)

  // 对于饱和度很低的颜色（如白色、灰色），增强饱和度以显示色相变化
  let enhancedSaturation = baseHsl.s
  if (baseHsl.s < 20) {
    enhancedSaturation = Math.max(30, baseHsl.s) // 最低30%饱和度
  }

  // 生成色相渐变的关键点（-180到180度）
  const gradientStops = []
  const hueSteps = [-180, -120, -60, 0, 60, 120, 180]

  for (let i = 0; i < hueSteps.length; i++) {
    const hueOffset = hueSteps[i]!
    const newHue = (baseHsl.h + hueOffset + 360) % 360
    // 使用增强的饱和度来确保色相变化可见
    const color = hslToHex(newHue, enhancedSaturation, baseHsl.l)
    const percentage = ((hueOffset + 180) / 360) * 100
    gradientStops.push(`${color} ${percentage.toFixed(1)}%`)
  }

  return `linear-gradient(to right, ${gradientStops.join(', ')})`
})

// 动态注入色相滑块样式
function injectHueSliderStyle() {
  // 获取当前渐变
  const gradient = dynamicHueGradient.value

  // 方法1：通过全局样式注入
  const existingStyle = document.getElementById('hue-slider-dynamic-style')
  if (existingStyle) {
    existingStyle.remove()
  }

  const style = document.createElement('style')
  style.id = 'hue-slider-dynamic-style'
  style.textContent = `
    .hue-slider .hue-slider-wrapper .el-slider__runway {
      height: 6px !important;
      background: ${gradient} !important;
    }
    .hue-slider-component .el-slider__runway {
      height: 6px !important;
      background: ${gradient} !important;
    }
  `
  document.head.appendChild(style)

  // 方法2：直接操作DOM元素（作为备用）
  nextTick(() => {
    if (hueSliderWrapperRef.value) {
      const runwayElement = hueSliderWrapperRef.value.querySelector(
        '.el-slider__runway'
      ) as HTMLElement
      if (runwayElement) {
        runwayElement.style.background = gradient
        runwayElement.style.height = '6px'
      }
    }
  })
}

// 重置所有值
function resetValues() {
  emit('update:hue', 0)
  emit('update:saturation', 0)
  emit('update:brightness', 0)
  emit('update:textureStrength', 0.7)
  emit('update:baseColor', '#FFFAF0')
}

// 监听基础色变化，更新样式
onMounted(() => {
  nextTick(() => {
    injectHueSliderStyle()
  })
})

// 监听基础色变化
watch(
  () => props.baseColor,
  () => {
    nextTick(() => {
      injectHueSliderStyle()
    })
  },
  { immediate: true }
)
</script>

<style scoped>
.panel-card {
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.panel-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 6px;
  color: #333;
}

.slider-container {
  margin-bottom: 10px;
}

.slider-label {
  font-size: 14px;
  margin-bottom: 6px;
}

.color-selector {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.reset-button-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

:deep(.el-checkbox) {
  height: 20px;
}

:deep(.el-slider__runway) {
  margin: 0;
  flex: 1;
  height: 6px;
}

:deep(.el-slider) {
  height: 20px !important;
}

:deep(.el-slider__bar) {
  border-radius: 3px;
  height: 6px;
  background-color: transparent;
}

/* 色相滑块包装器 */
.hue-slider-wrapper {
  position: relative;
}

/* 色相滑块轨道样式将通过动态样式注入 */

/* 通用滑块样式 - 排除色相滑块 */
.slider-container:not(.hue-slider) :deep(.el-slider__runway) {
  background: linear-gradient(to right, #797878, #e6e6e6) !important;
}

:deep(.el-slider__button) {
  border-color: #0052d9;
  box-shadow: 0 0 2px rgba(0, 82, 217, 0.5);
  width: 14px;
  height: 14px;
}
</style>
