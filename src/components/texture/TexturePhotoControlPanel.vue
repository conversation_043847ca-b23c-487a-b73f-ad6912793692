<template>
  <div class="panel-card">
    <div class="panel-title">微笑控制</div>

    <div class="panel-section checkbox-section">
      <div class="checkbox-item">
        <div class="section-label">显示纹理</div>
        <el-checkbox v-model="showTexture" class="custom-checkbox"></el-checkbox>
      </div>
      <div class="checkbox-item">
        <div class="section-label">显示框线</div>
        <el-checkbox v-model="showSmileFrame" class="custom-checkbox"></el-checkbox>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useTextureStore } from '@/store/texture'
import { storeToRefs } from 'pinia'

// 获取纹理Store
const textureStore = useTextureStore()
const { showTexture } = storeToRefs(textureStore)

// Props - 只需要showSmileFrame
const props = defineProps({
  showSmileFrame: {
    type: Boolean,
    required: true
  }
})

// Emits - 只需要update:showSmileFrame
const emit = defineEmits(['update:showSmileFrame'])

// 计算属性，用于双向绑定
const showSmileFrame = computed({
  get: () => props.showSmileFrame,
  set: (value) => emit('update:showSmileFrame', value)
})
</script>

<style scoped>
.panel-card {
  background: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.panel-section {
  margin-bottom: 12px;
}

.section-label {
  font-size: 15px;
  color: #333;
  font-weight: normal;
}

/* 复选框样式 */
.checkbox-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-bottom: 0;
}

.checkbox-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-checkbox) {
  height: 20px;
}
</style>
